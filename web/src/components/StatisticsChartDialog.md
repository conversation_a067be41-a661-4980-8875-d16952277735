# 统计图表弹出层组件使用说明

## 概述

`StatisticsChartDialog` 是一个独立的统计图表弹出层组件，用于展示不同维度的统计数据。支持根据 `id` 和 `target_type` 参数展示相应的图表数据，使用 tab 切换不同图表类型。

## 功能特性

- ✅ 独立组件，可在多个页面重复使用
- ✅ 支持三种目标类型：APP(1)、广告(2)、广告计划(3)
- ✅ 多图表展示，支持 tab 切换
- ✅ 广告计划类型支持关联广告数据合并展示
- ✅ 多条曲线展示，不同颜色区分
- ✅ 响应式设计，支持移动端
- ✅ 加载状态和错误处理

## 使用方法

### 1. 导入组件

```vue
<script setup>
import StatisticsChartDialog from '@/components/StatisticsChartDialog.vue'
</script>
```

### 2. 在模板中使用

```vue
<template>
  <!-- 统计图表弹出层 -->
  <StatisticsChartDialog
    v-model="statisticsChartVisible"
    :target-id="statisticsChartTargetId"
    :target-type="3"
  />
</template>
```

### 3. 添加响应式数据

```javascript
// 统计图表相关
const statisticsChartVisible = ref(false)
const statisticsChartTargetId = ref(null)
```

### 4. 添加打开方法

```javascript
// 打开统计图表
const openStatisticsChart = (item) => {
  statisticsChartTargetId.value = item.ID
  statisticsChartVisible.value = true
}
```

## Props 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | Boolean | false | 控制弹出层显示/隐藏 |
| targetId | Number/String | null | 目标ID（统计目标字段） |
| targetType | Number | 1 | 目标类型：1-APP，2-广告，3-广告计划 |

## 图表类型配置

### 默认图表（所有类型）
- 广告展示数 (kind: 2001)
- 广告点击数 (kind: 2002)

### APP 类型 (target_type=1)
- 日活用户数 (kind: 1001)
- 广告展示数 (kind: 2001)
- 广告点击数 (kind: 2002)

### 广告类型 (target_type=2)
- 广告展示数 (kind: 2001)
- 广告点击数 (kind: 2002)

### 广告计划类型 (target_type=3)
- 广告展示数 (kind: 2001) - 包含计划和关联广告数据
- 广告点击数 (kind: 2002) - 包含计划和关联广告数据

## 数据处理逻辑

### 广告计划数据合并

当 `target_type=3` 时，组件会：

1. 获取广告计划详情，解析关联的广告ID列表
2. 并行加载广告计划自身的统计数据
3. 并行加载所有关联广告的统计数据
4. 按日期合并数据，生成多条曲线：
   - 广告计划数据（蓝色曲线）
   - 每个关联广告数据（不同颜色曲线）

### 数据格式

组件期望的数据格式：
```javascript
{
  labels: ['2025-01-01', '2025-01-02', ...], // 日期数组
  datasets: [
    {
      label: '广告计划',
      data: [100, 120, 150, ...], // 数值数组
      borderColor: '#409EFF',
      backgroundColor: '#409EFF20'
    },
    {
      label: '广告 1',
      data: [80, 90, 110, ...],
      borderColor: '#67C23A',
      backgroundColor: '#67C23A20'
    }
    // ... 更多数据系列
  ]
}
```

## 样式定制

组件支持深色模式，会自动适配当前主题。主要样式类：

- `.statistics-chart-dialog` - 主容器
- `.chart-container` - 图表容器
- `.loading-container` - 加载状态
- `.error-container` - 错误状态
- `.chart-content` - 图表内容
- `.chart-wrapper` - 图表包装器

## 错误处理

组件包含完整的错误处理机制：

- 网络请求失败
- 数据解析错误
- 参数验证错误
- 提供重新加载功能

## 注意事项

1. 确保后端 API 支持相应的查询参数
2. 广告计划的 `ad_ids` 字段应为 JSON 格式的数组
3. 统计数据的 `day` 字段应为标准日期格式
4. 组件依赖 Element Plus 和 ECharts

## 在 Campaign 页面中的集成

已在 `campaign.vue` 页面中集成：

- 表格视图：计划名称列可点击
- 卡片视图：计划名称标题可点击
- 点击后打开统计图表弹出层，展示该广告计划的统计数据

## API 依赖

组件依赖以下 API：

- `getDashubList` - 获取统计数据列表
- `findCampaign` - 获取广告计划详情（用于获取关联广告ID）
- `getChartData` - 获取图表数据（可选，用于优化）
