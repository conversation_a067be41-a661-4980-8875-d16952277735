<!--
  统计图表弹出层组件
  支持根据 id 和 target_type 展示不同维度的统计图表
  使用 tab 切换不同图表类型
-->

<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="80%"
    :before-close="handleClose"
    destroy-on-close
    class="statistics-chart-dialog"
  >
    <div class="chart-container">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <el-icon class="is-loading">
          <Loading />
        </el-icon>
        <span>加载图表数据中...</span>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <el-icon>
          <Warning />
        </el-icon>
        <span>{{ error }}</span>
        <el-button type="primary" @click="loadData">重新加载</el-button>
      </div>

      <!-- 图表内容 -->
      <div v-else class="chart-content">
        <!-- Tab 切换 -->
        <el-tabs v-model="activeTab" @tab-change="handleTabChange">
          <el-tab-pane
            v-for="chart in chartTabs"
            :key="chart.key"
            :label="chart.label"
            :name="chart.key"
          >
            <div class="chart-wrapper">
              <!-- 图表标题和描述 -->
              <div class="chart-header">
                <h3 class="chart-title">{{ chart.title }}</h3>
                <p v-if="chart.description" class="chart-description">
                  {{ chart.description }}
                </p>
              </div>

              <!-- 图表组件 -->
              <div class="chart-body">
                <ChartsLine
                  :data="chart.data"
                  :title="chart.title"
                  height="400px"
                />
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'
import ChartsLine from '@/view/dashboard/components/charts-line.vue'
import { getDashubList, getChartData } from '@/api/meta/dashub'
import { findCampaign } from '@/api/meta/campaign'

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  targetId: {
    type: [Number, String],
    default: null
  },
  targetType: {
    type: Number,
    default: 1 // 1:APP，2:广告，3:广告计划
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const error = ref('')
const activeTab = ref('impressions')
const chartTabs = ref([])
const rawData = ref([])

// 计算属性
const dialogTitle = computed(() => {
  const typeNames = {
    1: 'APP',
    2: '广告',
    3: '广告计划'
  }
  return `${typeNames[props.targetType] || ''}统计图表 (ID: ${props.targetId})`
})

// 图表类型配置
const getChartConfig = () => {
  const baseCharts = [
    {
      key: 'impressions',
      label: '展示数据',
      title: '广告展示数',
      description: '广告展示次数随时间变化趋势',
      kind: 2001, // DASHUB_AD_IMPRESSIONS
      data: {}
    },
    {
      key: 'clicks',
      label: '点击数据',
      title: '广告点击数',
      description: '广告点击次数随时间变化趋势',
      kind: 2002, // DASHUB_AD_CLICKS
      data: {}
    }
  ]

  // 根据 target_type 添加特定图表
  if (props.targetType === 1) {
    // APP 类型添加日活用户数
    baseCharts.unshift({
      key: 'active_users',
      label: '活跃用户',
      title: '日活用户数',
      description: '每日活跃用户数量变化趋势',
      kind: 1001, // DASHUB_DAILY_ACTIVE_USERS
      data: {}
    })
  }

  return baseCharts
}

// 处理关闭
const handleClose = () => {
  visible.value = false
  // 重置状态
  loading.value = false
  error.value = ''
  activeTab.value = 'impressions'
  chartTabs.value = []
  rawData.value = []
}

// Tab 切换处理
const handleTabChange = (tabName) => {
  activeTab.value = tabName
}

// 加载数据
const loadData = async () => {
  if (!props.targetId) {
    error.value = '缺少目标ID参数'
    return
  }

  loading.value = true
  error.value = ''

  try {
    // 初始化图表配置
    chartTabs.value = getChartConfig()

    if (props.targetType === 3) {
      // 广告计划类型：需要加载计划本身和关联广告的数据
      await loadCampaignData()
    } else {
      // 其他类型：直接加载数据
      await loadDirectData()
    }

    // 处理图表数据
    processChartData()
  } catch (err) {
    console.error('加载数据失败:', err)
    error.value = err.message || '加载数据失败'
    ElMessage.error(error.value)
  } finally {
    loading.value = false
  }
}

// 加载广告计划数据（包括关联广告）
const loadCampaignData = async () => {
  try {
    // 1. 获取广告计划详情以获取关联的广告ID列表
    const campaignRes = await findCampaign({ ID: props.targetId })

    if (campaignRes.code !== 0) {
      throw new Error('获取广告计划详情失败')
    }

    const campaign = campaignRes.data
    let adIds = []

    // 解析广告ID列表
    if (campaign.ad_ids) {
      try {
        adIds = typeof campaign.ad_ids === 'string'
          ? JSON.parse(campaign.ad_ids)
          : campaign.ad_ids
      } catch (e) {
        console.warn('解析广告ID列表失败:', e)
        adIds = []
      }
    }

    // 2. 并行加载广告计划和关联广告的数据
    const allPromises = []

    // 加载广告计划数据
    chartTabs.value.forEach(chart => {
      allPromises.push(
        getDashubList({
          target_type: props.targetType,
          target: props.targetId,
          kind: chart.kind,
          page: 1,
          pageSize: 100
        }).then(res => ({ type: 'campaign', chart: chart.key, data: res }))
      )
    })

    // 加载关联广告数据
    if (adIds.length > 0) {
      adIds.forEach(adId => {
        chartTabs.value.forEach(chart => {
          allPromises.push(
            getDashubList({
              target_type: 2, // 广告类型
              target: adId,
              kind: chart.kind,
              page: 1,
              pageSize: 100
            }).then(res => ({ type: 'ad', adId, chart: chart.key, data: res }))
          )
        })
      })
    }

    const results = await Promise.all(allPromises)

    // 3. 整理数据结构
    rawData.value = {
      campaign: {},
      ads: {},
      adIds: adIds,
      campaignInfo: campaign
    }

    results.forEach(result => {
      if (result.type === 'campaign') {
        rawData.value.campaign[result.chart] = result.data
      } else if (result.type === 'ad') {
        if (!rawData.value.ads[result.chart]) {
          rawData.value.ads[result.chart] = {}
        }
        rawData.value.ads[result.chart][result.adId] = result.data
      }
    })

  } catch (err) {
    console.error('加载广告计划数据失败:', err)
    throw err
  }
}

// 加载直接数据
const loadDirectData = async () => {
  const promises = chartTabs.value.map(chart => 
    getDashubList({
      target_type: props.targetType,
      target: props.targetId,
      kind: chart.kind,
      page: 1,
      pageSize: 100
    })
  )

  const results = await Promise.all(promises)
  rawData.value = results
}

// 处理图表数据
const processChartData = () => {
  if (props.targetType === 3) {
    // 广告计划：合并计划和广告数据
    processCampaignChartData()
  } else {
    // 其他类型：直接处理
    processDirectChartData()
  }
}

// 处理广告计划图表数据
const processCampaignChartData = () => {
  const { campaign: campaignData, ads: adsData, adIds } = rawData.value

  chartTabs.value.forEach(chart => {
    // 获取广告计划自身数据
    const campaignChartData = campaignData[chart.key]?.data?.list || []

    // 获取关联广告数据
    const adChartData = []
    if (adIds && adIds.length > 0) {
      adIds.forEach(adId => {
        const adData = adsData[chart.key]?.[adId]?.data?.list || []
        adChartData.push(...adData.map(item => ({ ...item, adId })))
      })
    }

    // 合并广告计划和广告数据
    const allData = [
      ...campaignChartData.map(item => ({ ...item, source: 'campaign' })),
      ...adChartData.map(item => ({ ...item, source: 'ad' }))
    ]

    // 按日期分组合并数据
    const mergedData = mergeDataByDate(allData, adIds)

    chart.data = formatCampaignChartData(mergedData, adIds)
  })
}

// 按日期合并数据
const mergeDataByDate = (dataList, adIds) => {
  const dateMap = {}

  dataList.forEach(item => {
    const date = item.day
    if (!dateMap[date]) {
      dateMap[date] = {
        date,
        campaign: 0,
        ads: {},
        total: 0
      }
      // 初始化每个广告的数据
      if (adIds) {
        adIds.forEach(adId => {
          dateMap[date].ads[adId] = 0
        })
      }
    }

    // 根据数据源分类数据
    if (item.source === 'campaign') {
      // 广告计划数据
      dateMap[date].campaign += item.nums || 0
    } else if (item.source === 'ad') {
      // 广告数据
      const adId = item.adId
      if (dateMap[date].ads[adId] !== undefined) {
        dateMap[date].ads[adId] += item.nums || 0
      }
    }

    dateMap[date].total += item.nums || 0
  })

  return Object.values(dateMap).sort((a, b) => a.date.localeCompare(b.date))
}

// 格式化广告计划图表数据（多条曲线）
const formatCampaignChartData = (mergedData, adIds) => {
  if (!mergedData || mergedData.length === 0) {
    return {
      labels: [],
      datasets: []
    }
  }

  const labels = mergedData.map(item => item.date)
  const datasets = []

  // 添加广告计划数据曲线
  datasets.push({
    label: '广告计划',
    data: mergedData.map(item => item.campaign),
    borderColor: '#409EFF',
    backgroundColor: '#409EFF20'
  })

  // 添加每个广告的数据曲线
  if (adIds && adIds.length > 0) {
    const colors = ['#67C23A', '#E6A23C', '#F56C6C', '#909399', '#00D2FF', '#FF6B6B']
    adIds.forEach((adId, index) => {
      datasets.push({
        label: `广告 ${adId}`,
        data: mergedData.map(item => item.ads[adId] || 0),
        borderColor: colors[index % colors.length],
        backgroundColor: `${colors[index % colors.length]}20`
      })
    })
  }

  return {
    labels,
    datasets
  }
}

// 处理直接图表数据
const processDirectChartData = () => {
  chartTabs.value.forEach((chart, index) => {
    const data = rawData.value[index]?.data?.list || []
    chart.data = formatChartData(data)
  })
}

// 格式化图表数据
const formatChartData = (data) => {
  if (!data || data.length === 0) {
    return {
      labels: [],
      datasets: []
    }
  }

  // 按日期分组并排序
  const groupedData = {}
  data.forEach(item => {
    const date = item.day
    if (!groupedData[date]) {
      groupedData[date] = 0
    }
    groupedData[date] += item.nums || 0
  })

  const sortedDates = Object.keys(groupedData).sort()
  const values = sortedDates.map(date => groupedData[date])

  return {
    labels: sortedDates,
    datasets: [{
      label: '数量',
      data: values
    }]
  }
}

// 监听 props 变化
watch(() => [props.modelValue, props.targetId, props.targetType], 
  ([newVisible, newTargetId, newTargetType]) => {
    if (newVisible && newTargetId) {
      nextTick(() => {
        loadData()
      })
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.statistics-chart-dialog {
  .chart-container {
    min-height: 500px;
  }

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    gap: 16px;
    color: #666;

    .el-icon {
      font-size: 48px;
    }
  }

  .chart-content {
    .chart-wrapper {
      .chart-header {
        margin-bottom: 20px;
        text-align: center;

        .chart-title {
          font-size: 18px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 8px 0;
        }

        .chart-description {
          font-size: 14px;
          color: #909399;
          margin: 0;
        }
      }

      .chart-body {
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      }
    }
  }

  .dialog-footer {
    text-align: right;
  }
}

// 深色模式适配
.dark .statistics-chart-dialog {
  .chart-wrapper .chart-header .chart-title {
    color: #e5eaf3;
  }

  .chart-wrapper .chart-body {
    background: #1d1e1f;
  }
}
</style>
